<template>
	<div class="winner-publicity-container">
		<div class="bg-[#fff] p-5 mb-3">
			<!-- 页面标题 -->
			<div class="page-header">
				<div class="company-info">
					<el-icon class="company-icon"><office-building /></el-icon>
					<span class="company-name">成都运动技科技有限公司</span>
					<el-tag type="info" size="small" class="status-tag">公示中</el-tag>
				</div>
			</div>

			<!-- 标签栏 -->
			<StageTabs
				:tabs="tabs"
				v-model="activeTabIndex"
			/>

			<!-- 中标信息表格 -->
			<el-table
				:data="winnerTableData"
				style="width: 100%"
				border
				class="winner-table mt-4"
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="供应商名称"
					prop="supplierName"
					min-width="200"
				/>
				<el-table-column
					label="联系人"
					prop="contactPerson"
					width="120"
					align="center"
				/>
				<el-table-column
					label="联系方式"
					prop="contactPhone"
					width="140"
					align="center"
				/>
				<el-table-column
					label="成交价格"
					prop="totalPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span>{{ formatPrice(row.totalPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="操作"
					width="140"
					align="center"
				>
					<template #default="{ row }">
						<el-button
							type="text"
							@click="handleViewDetail(row)"
						>
							查看中标明细
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 公示要求 -->
		<div class="publicity-requirements-section">
			<div class="section-header">
				<div class="header-title">公示要求</div>
			</div>

			<el-form label-width="120px" class="publicity-form">
				<el-form-item label="是否发布公告" required>
					<el-radio-group v-model="publicityForm.publishType">
						<el-radio label="system">系统发布公告</el-radio>
						<el-radio label="manual">不发布中标公告</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-row>
					<el-col :span="12">
						<el-form-item label="公示起止时间" required>
							<el-date-picker
								v-model="publicityForm.time"
								type="datetimerange"
								placeholder=""
								format="YYYY-MM-DD HH:mm:ss"
								value-format="YYYY-MM-DD HH:mm:ss"
								range-separator="至"
								start-placeholder="开始时间"
								end-placeholder="结束时间"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
<!--					<el-col :span="8">-->
<!--						<el-form-item label="结束时间">-->
<!--							<el-date-picker-->
<!--								v-model="publicityForm.endTime"-->
<!--								type="datetime"-->
<!--								placeholder=""-->
<!--								format="YYYY-MM-DD HH:mm"-->
<!--								value-format="YYYY-MM-DD HH:mm:ss"-->
<!--								style="width: 180px"-->
<!--							/>-->
<!--						</el-form-item>-->
<!--					</el-col>-->
				</el-row>

				<el-form-item label="公示标题" required>
					<el-input
						v-model="publicityForm.title"
						placeholder="请输入"
						style="width: 100%"
					/>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公示详情 -->
		<div class="publicity-details-section">
			<div class="section-header">
				<div class="header-title">公示详情</div>
			</div>

			<el-form label-width="120px" class="publicity-form">
				<el-form-item label="引用模版" required>
					<el-select
						v-model="publicityForm.template"
						placeholder="公告模版"
						style="width: 200px"
					>
						<el-option
							v-for="template in templateOptions"
							:key="template.value"
							:label="template.label"
							:value="template.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="公告内容" required>
					<el-link type="primary" @click="handleEditContent">
						<el-icon class="mr-[6px]"><edit-pen /></el-icon>
						编辑
					</el-link>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公示附件 -->
		<div class="publicity-attachments-section">
			<div class="section-header">
				<div class="header-title">公示附件</div>
			</div>

			<el-form label-width="120px" class="publicity-form">
				<el-form-item label="公示附件">
					<el-upload
						ref="publicityUploadRef"
						:file-list="publicityFileList"
						:before-upload="handleBeforeUpload"
						:on-success="handleUploadSuccess"
						:on-error="handleUploadError"
						:on-remove="handleRemoveFile"
						:show-file-list="true"
						action="/api/upload"
						accept=".pdf,.doc,.docx,.xls,.xlsx"
					>
						<el-button>
							上传文件
						</el-button>
					</el-upload>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公示平台 -->
		<div class="publicity-platform-section">
			<div class="section-header">
				<div class="header-title">公示平台</div>
			</div>

			<el-form label-width="120px" class="publicity-form">
				<el-form-item label="公示平台选择">
					<div class="platform-checkboxes">
						<el-checkbox-group v-model="publicityForm.platforms">
							<el-checkbox
								v-for="platform in platformOptions"
								:key="platform.value"
								:label="platform.value"
							>
								{{ platform.label }}
							</el-checkbox>
						</el-checkbox-group>
					</div>
				</el-form-item>
			</el-form>
		</div>
	</div>

	<!-- 公告内容编辑抽屉 -->
	<AnnouncementEditor ref="announcementEditorRef" @save="handleSaveContent" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { OfficeBuilding } from '@element-plus/icons-vue';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import AnnouncementEditor from '../AwardResult/components/AnnouncementEditor.vue';

// 抽屉组件引用
const announcementEditorRef = ref();

// 上传组件引用
const publicityUploadRef = ref();

const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
	{ key: '5', label: '标段05: 进度计划制定' },
	{ key: '6', label: '标段06: 质量控制方案' },
	{ key: '7', label: '标段07: 质量控制方案' },
	{ key: '8', label: '标段08' },
];

const activeTabIndex = ref(0);

// 中标信息表格数据
const winnerTableData = ref([
	{
		id: '1',
		supplierName: '成都运动技科技有限公司',
		contactPerson: '张三',
		contactPhone: '183 2222 4444',
		totalPrice: 2000.00,
	}
]);

// 公示表单数据
const publicityForm = reactive({
	publishType: 'system', // 'system' | 'manual'
	deadline: '',
	startTime: '',
	endTime: '',
	title: '',
	template: '',
	content: '',
	platforms: [],
});

// 模板选项
const templateOptions = ref([
	{ label: '公告模版', value: 'standard' },
]);

// 平台选项
const platformOptions = ref([
	{ label: '公示平台选择', value: 'procurement' },
	{ label: '优质采购采购平台', value: 'quality-procurement' },
	{ label: '中国招标网', value: 'china-tender' },
]);

// 公示附件文件列表
const publicityFileList = ref([]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理上传前验证
function handleBeforeUpload(file: File) {
	const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type);
	const isLt10M = file.size / 1024 / 1024 < 10;

	if (!isValidType) {
		ElMessage.error('只能上传 PDF、Word、Excel 格式的文件！');
		return false;
	}
	if (!isLt10M) {
		ElMessage.error('文件大小不能超过 10MB！');
		return false;
	}
	return true;
}

// 处理上传成功
function handleUploadSuccess(response: any, file: any) {
	ElMessage.success('公示附件上传成功');
	console.log('上传成功:', response, file);
}

// 处理上传失败
function handleUploadError(error: any) {
	ElMessage.error('公示附件上传失败，请重试');
	console.error('上传失败:', error);
}

// 处理移除文件
function handleRemoveFile(file: any) {
	ElMessage.info('已移除文件');
	console.log('移除文件:', file);
}

// 处理查看中标明细
function handleViewDetail(row: any) {
	ElMessage.info(`查看 ${row.supplierName} 的中标明细功能开发中`);
}

// 处理编辑公告内容
function handleEditContent() {
	announcementEditorRef.value?.show(publicityForm.content);
}

// 处理保存公告内容
function handleSaveContent(content: string) {
	publicityForm.content = content;
	ElMessage.success('公告内容保存成功');
}
</script>

<style lang="scss" scoped>
.winner-publicity-container {
	//padding: 20px;
	//background: #f5f7fa;
	//min-height: 100vh;
}

.page-header {

	margin-bottom: 16px;

	.company-info {
		display: flex;
		align-items: center;
		gap: 8px;

		.company-icon {
			color: #0069ff;
			font-size: 18px;
		}

		.company-name {
			font-size: 18px;
			font-weight: 600;
			color: #1d2129;
		}

		.status-tag {
			margin-left: 8px;
		}
	}
}

.winner-table-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platform-section {
	margin-bottom: 12px;
	background: #fff;
	border-radius: 6px;
	padding: 20px;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		.header-title {
			font-size: 16px;
			font-weight: 600;
			color: #1d2129;
		}
	}
}

.publicity-form {
	:deep(.el-form-item) {
		margin-bottom: 20px;
	}

	:deep(.el-form-item__label) {
		//color: #86909c;
		//font-weight: 400;
	}
}

.winner-table {
	:deep(.el-table__header) {
		th {
			background: #f5f7fa;
			padding: 12px 0;
			border-bottom: 1px solid #ebeef5;

			.cell {
				color: #505762;
				font-size: 14px;
				font-weight: 500;
			}
		}
	}

	:deep(.el-table__body) {
		tr {
			td {
				border-bottom: 1px solid #ebeef5;
				padding: 12px 0;
			}
		}
	}
}

.platform-checkboxes {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

:deep(.el-range-input) {
	background: #fff;
}
</style>
